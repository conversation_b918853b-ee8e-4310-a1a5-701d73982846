# NumPy + Gradio Application

Um projeto Python moderno que demonstra o uso de NumPy para computação numérica e Gradio para criar interfaces web interativas.

## Características

- 🔢 **NumPy**: Computação numérica eficiente
- 🌐 **Gradio**: Interface web interativa e fácil de usar
- 📐 **Resolvedor de Equações**: Solução completa para equações quadráticas
- 📦 **uv**: Gerenciador de pacotes Python rápido e moderno
- 🎨 **Matplotlib**: Visualização de dados
- 🧪 **Pytest**: Framework de testes

## Pré-requisitos

- Python 3.8 ou superior
- uv (gerenciador de pacotes)

## Instalação

### 1. Instalar uv (se ainda não tiver)

```bash
# No macOS/Linux
curl -LsSf https://astral.sh/uv/install.sh | sh

# No Windows
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"
```

### 2. Clonar e configurar o projeto

```bash
# Clonar o repositório
git clone <seu-repositorio>
cd numpy-gradio-app

# Criar ambiente virtual e instalar dependências
uv venv
source .venv/bin/activate  # No Windows: .venv\Scripts\activate
uv pip install -e .
```

## Uso

### Executar a aplicação

```bash
# Ativar o ambiente virtual
source .venv/bin/activate  # No Windows: .venv\Scripts\activate

# Executar a aplicação
python src/main.py
```

A aplicação será executada em `http://localhost:7860`

### Desenvolvimento

```bash
# Instalar dependências de desenvolvimento
uv pip install -e ".[dev]"

# Executar testes
pytest

# Formatação de código
black src/

# Verificação de tipos
mypy src/
```

## Estrutura do Projeto

```
numpy-gradio-app/
├── src/
│   ├── __init__.py          # Pacote Python
│   ├── main.py              # Aplicação principal
│   └── quadratic_solver.py  # Módulo para equações quadráticas
├── docs/
│   └── quadratic_equations.md  # Documentação das equações
├── tests/                   # Testes
├── pyproject.toml          # Configuração do projeto
├── README.md              # Este arquivo
└── .gitignore             # Arquivos ignorados pelo Git
```

## Funcionalidades

A aplicação demonstra:

1. **🎲 Arrays Aleatórios**: Geração e análise estatística de arrays com diferentes distribuições
2. **🔢 Operações com Matrizes**: Cálculos matemáticos e manipulação de matrizes
3. **📐 Equações Quadráticas**: Resolvedor completo para equações de segundo grau
4. **🌊 Análise de Fourier**: Transformada de Fourier e processamento de sinais
5. **🎨 Visualização**: Gráficos interativos usando Matplotlib
6. **🌐 Interface Web**: Interface amigável e responsiva com Gradio

## Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## Licença

Este projeto está licenciado sob a Licença MIT - veja o arquivo [LICENSE](LICENSE) para detalhes.

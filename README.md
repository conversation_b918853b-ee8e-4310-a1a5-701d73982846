# NumPy + Gradio Application

Um projeto Python moderno que demonstra o uso de NumPy para computação numérica e Gradio para criar interfaces web interativas.

## Características

- 🔢 **NumPy**: Computação numérica eficiente
- 🌐 **Gradio**: Interface web interativa e fácil de usar
- 📦 **uv**: Gerenciador de pacotes Python rápido e moderno
- 🎨 **Matplotlib**: Visualização de dados
- 🧪 **Pytest**: Framework de testes

## Pré-requisitos

- Python 3.8 ou superior
- uv (gerenciador de pacotes)

## Instalação

### 1. Instalar uv (se ainda não tiver)

```bash
# No macOS/Linux
curl -LsSf https://astral.sh/uv/install.sh | sh

# No Windows
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"
```

### 2. Clonar e configurar o projeto

```bash
# Clonar o repositório
git clone <seu-repositorio>
cd numpy-gradio-app

# Criar ambiente virtual e instalar dependências
uv venv
source .venv/bin/activate  # No Windows: .venv\Scripts\activate
uv pip install -e .
```

## Uso

### Executar a aplicação

```bash
# Ativar o ambiente virtual
source .venv/bin/activate  # No Windows: .venv\Scripts\activate

# Executar a aplicação
python src/main.py
```

A aplicação será executada em `http://localhost:7860`

### Desenvolvimento

```bash
# Instalar dependências de desenvolvimento
uv pip install -e ".[dev]"

# Executar testes
pytest

# Formatação de código
black src/

# Verificação de tipos
mypy src/
```

## Estrutura do Projeto

```
numpy-gradio-app/
├── src/
│   └── main.py          # Aplicação principal
├── tests/               # Testes
├── pyproject.toml       # Configuração do projeto
├── README.md           # Este arquivo
└── .gitignore          # Arquivos ignorados pelo Git
```

## Funcionalidades

A aplicação demonstra:

1. **Operações com NumPy**: Cálculos matemáticos e manipulação de arrays
2. **Interface Gradio**: Interface web interativa para visualizar resultados
3. **Visualização**: Gráficos usando Matplotlib
4. **Exemplos práticos**: Casos de uso comuns com NumPy

## Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## Licença

Este projeto está licenciado sob a Licença MIT - veja o arquivo [LICENSE](LICENSE) para detalhes.

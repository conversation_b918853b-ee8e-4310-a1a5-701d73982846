[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "numpy-gradio-app"
version = "0.1.0"
description = "A Python application using NumPy and Gradio"
readme = "README.md"
requires-python = ">=3.8"
authors = [
    {name = "Your Name", email = "<EMAIL>"},
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]
dependencies = [
    "numpy",
    "gradio[mcp]",
    "matplotlib>=3.5.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "black>=22.0.0",
    "flake8>=5.0.0",
    "mypy>=1.0.0",
]

[project.urls]
Homepage = "https://github.com/yourusername/numpy-gradio-app"
Repository = "https://github.com/yourusername/numpy-gradio-app"
Issues = "https://github.com/yourusername/numpy-gradio-app/issues"

[tool.black]
line-length = 88
target-version = ['py38']

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[project.scripts]
numpy-gradio-app = "src.main:main"

[tool.hatch.build.targets.wheel]
packages = ["src"]

#!/usr/bin/env python3
"""
Servidor MCP integrado com Google Gemini usando Gradio.

Este servidor combina o poder do Gemini para interpretação de linguagem natural
com o servidor MCP para cálculos matemáticos precisos de equações quadráticas.

Uso:
    python gemini_mcp_server.py

O servidor estará disponível em:
    http://localhost:7861
"""

import os
import json
import gradio as gr
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, Tuple, List, Union, Optional

# Importar dotenv se disponível
try:
    from dotenv import load_dotenv
    load_dotenv()
    HAS_DOTENV = True
except ImportError:
    HAS_DOTENV = False
    print("⚠️  python-dotenv não encontrado. Usando variáveis de ambiente do sistema.")

# Importar Google Gemini
try:
    import google.generativeai as genai
    HAS_GEMINI = True
except ImportError:
    HAS_GEMINI = False
    print("⚠️  google-generativeai não encontrado. Funcionalidades do Gemini desabilitadas.")

# Importar módulo de equações quadráticas
try:
    from src.quadratic_solver import solve_quadratic, format_solution, plot_quadratic
    HAS_QUADRATIC_SOLVER = True
except ImportError:
    HAS_QUADRATIC_SOLVER = False
    print("⚠️  Módulo src.quadratic_solver não encontrado. Usando implementação básica.")
    
    # Implementação básica
    def solve_quadratic(a, b, c):
        result = {'coefficients': {'a': a, 'b': b, 'c': c}, 'equation_type': 'quadratic' if a != 0 else 'linear', 'discriminant': None, 'roots': [], 'roots_type': '', 'vertex': None, 'axis_of_symmetry': None, 'y_intercept': c, 'concavity': '', 'factored_form': '', 'has_real_roots': False}
        if a == 0:
            if b == 0:
                result['equation_type'] = 'invalid'
                result['roots_type'] = 'sem solução válida'
            else:
                result['equation_type'] = 'linear'
                root = -c / b
                result['roots'] = [root]
                result['roots_type'] = 'uma raiz real'
                result['has_real_roots'] = True
        else:
            discriminant = b**2 - 4*a*c
            result['discriminant'] = discriminant
            result['vertex'] = (-b/(2*a), a*(-b/(2*a))**2 + b*(-b/(2*a)) + c)
            result['axis_of_symmetry'] = -b/(2*a)
            result['concavity'] = 'para cima' if a > 0 else 'para baixo'
            if discriminant > 0:
                x1 = (-b + discriminant**0.5) / (2*a)
                x2 = (-b - discriminant**0.5) / (2*a)
                result['roots'] = [x1, x2]
                result['roots_type'] = 'duas raízes reais distintas'
                result['has_real_roots'] = True
                result['factored_form'] = f"{a}(x - {x1:.3f})(x - {x2:.3f})"
            elif discriminant == 0:
                x = -b / (2*a)
                result['roots'] = [x]
                result['roots_type'] = 'uma raiz real (raiz dupla)'
                result['has_real_roots'] = True
                result['factored_form'] = f"{a}(x - {x:.3f})²"
            else:
                real_part = -b / (2*a)
                imag_part = (-discriminant)**0.5 / (2*a)
                x1 = complex(real_part, imag_part)
                x2 = complex(real_part, -imag_part)
                result['roots'] = [x1, x2]
                result['roots_type'] = 'duas raízes complexas conjugadas'
                result['has_real_roots'] = False
        return result
    
    def format_solution(solution):
        a, b, c = solution['coefficients']['a'], solution['coefficients']['b'], solution['coefficients']['c']
        if solution['equation_type'] == 'invalid':
            return "❌ Equação inválida"
        elif solution['equation_type'] == 'linear':
            x = solution['roots'][0]
            return f"📐 Equação Linear: {b}x + {c} = 0\n✅ Solução: x = {x:.6f}"
        else:
            text = f"📐 Equação Quadrática: {a}x² + {b}x + {c} = 0\n"
            text += f"🔍 Discriminante: {solution['discriminant']:.6f}\n"
            text += f"✅ {solution['roots_type']}\n"
            if solution['has_real_roots']:
                for i, root in enumerate(solution['roots'], 1):
                    if isinstance(root, complex):
                        text += f"x{i} = {root.real:.6f} + {root.imag:.6f}i\n"
                    else:
                        text += f"x{i} = {root:.6f}\n"
            return text


class GeminiMCPServer:
    """Servidor MCP integrado com Gemini."""
    
    def __init__(self):
        self.gemini_model = None
        self.setup_gemini()
    
    def setup_gemini(self):
        """Configura o modelo Gemini."""
        if not HAS_GEMINI:
            print("❌ Google Gemini não disponível")
            return
        
        # Obter chave da API
        api_key = os.getenv("GOOGLE_API_KEY")
        if not api_key:
            print("❌ GOOGLE_API_KEY não encontrada no arquivo .env")
            return
        
        try:
            genai.configure(api_key=api_key)
            self.gemini_model = genai.GenerativeModel('gemini-pro')
            print("✅ Google Gemini configurado com sucesso")
        except Exception as e:
            print(f"❌ Erro ao configurar Gemini: {e}")
    
    def extract_coefficients_with_gemini(self, text: str) -> Optional[Tuple[float, float, float]]:
        """Extrai coeficientes usando Gemini."""
        if not self.gemini_model:
            return None
        
        prompt = f"""
Extraia os coeficientes a, b, c da equação quadrática no seguinte texto:
"{text}"

A equação deve estar na forma ax² + bx + c = 0 ou f(x) = ax² + bx + c

Responda APENAS com três números separados por vírgula, na ordem a,b,c
Exemplo: 1,-5,6

Se não conseguir identificar uma equação quadrática válida, responda: ERRO

Texto: {text}
"""
        
        try:
            response = self.gemini_model.generate_content(prompt)
            result = response.text.strip()
            
            if result == "ERRO":
                return None
            
            coeffs = result.split(',')
            if len(coeffs) == 3:
                a = float(coeffs[0].strip())
                b = float(coeffs[1].strip())
                c = float(coeffs[2].strip())
                return (a, b, c)
            else:
                return None
                
        except Exception as e:
            print(f"Erro ao extrair coeficientes com Gemini: {e}")
            return None
    
    def explain_with_gemini(self, equation_text: str, mcp_result: Dict) -> str:
        """Gera explicação didática usando Gemini."""
        if not self.gemini_model:
            return json.dumps(mcp_result, indent=2, ensure_ascii=False)
        
        prompt = f"""
Com base na seguinte solução de equação quadrática, forneça uma explicação didática e completa:

Equação original: {equation_text}
Resultado do cálculo:
{json.dumps(mcp_result, indent=2, ensure_ascii=False)}

Forneça uma explicação que inclua:
1. A equação na forma padrão
2. Explicação do discriminante e seu significado
3. Interpretação das raízes encontradas
4. Conceitos matemáticos relevantes
5. Verificação das soluções (se possível)

Seja didático, educativo e use emojis para tornar mais interessante.
Explique cada passo de forma clara para estudantes.
"""
        
        try:
            response = self.gemini_model.generate_content(prompt)
            return response.text
        except Exception as e:
            return f"Erro na explicação: {str(e)}\n\nResultado bruto:\n{json.dumps(mcp_result, indent=2, ensure_ascii=False)}"


# Instância global do servidor
gemini_server = GeminiMCPServer()


def solve_with_gemini_mcp(user_input: str) -> Tuple[str, str]:
    """
    Resolve equação quadrática usando Gemini + MCP.
    
    Args:
        user_input: Entrada do usuário em linguagem natural
    
    Returns:
        Tuple com explicação e resultado JSON
    """
    if not user_input.strip():
        return "❌ Por favor, digite uma equação quadrática.", "{}"
    
    # Tentar extrair coeficientes com Gemini
    coeffs = gemini_server.extract_coefficients_with_gemini(user_input)
    
    if not coeffs:
        # Fallback: tentar extrair manualmente
        try:
            # Buscar padrões simples como "x² - 5x + 6"
            import re
            pattern = r'([+-]?\d*\.?\d*)\s*x²?\s*([+-]?\d*\.?\d*)\s*x\s*([+-]?\d*\.?\d*)'
            match = re.search(pattern, user_input.replace('²', '').replace('^2', ''))
            
            if match:
                a_str, b_str, c_str = match.groups()
                a = float(a_str) if a_str and a_str not in ['+', '-'] else (1 if a_str != '-' else -1)
                b = float(b_str) if b_str and b_str not in ['+', '-'] else (1 if b_str == '+' else -1 if b_str == '-' else 0)
                c = float(c_str) if c_str and c_str not in ['+', '-'] else (1 if c_str == '+' else -1 if c_str == '-' else 0)
                coeffs = (a, b, c)
            else:
                return "❌ Não consegui identificar os coeficientes da equação. Tente um formato como 'x² - 5x + 6 = 0'", "{}"
        except:
            return "❌ Erro ao processar a equação. Verifique o formato.", "{}"
    
    a, b, c = coeffs
    
    # Resolver usando MCP
    try:
        mcp_result = solve_quadratic(a, b, c)
        
        # Gerar explicação com Gemini
        explanation = gemini_server.explain_with_gemini(user_input, mcp_result)
        
        # Resultado JSON para exibição
        json_result = json.dumps(mcp_result, indent=2, ensure_ascii=False)
        
        return explanation, json_result
        
    except Exception as e:
        return f"❌ Erro no cálculo: {str(e)}", "{}"


def solve_quadratic_mcp_tool(a: str, b: str, c: str) -> str:
    """Ferramenta MCP para resolver equação quadrática."""
    try:
        a_val = float(a)
        b_val = float(b)
        c_val = float(c)
        
        result = solve_quadratic(a_val, b_val, c_val)
        
        # Converter complexos para string
        for i, root in enumerate(result['roots']):
            if isinstance(root, complex):
                result['roots'][i] = f"{root.real:.6f} + {root.imag:.6f}i"
        
        return json.dumps(result, indent=2, ensure_ascii=False)
        
    except Exception as e:
        return json.dumps({"error": str(e)}, indent=2, ensure_ascii=False)


def create_gemini_mcp_interface():
    """Cria interface Gradio integrada com Gemini e MCP."""
    
    with gr.Blocks(
        title="Gemini + MCP - Equações Quadráticas",
        theme=gr.themes.Soft(),
        css="""
        .gradio-container {
            max-width: 1400px !important;
        }
        .gemini-header {
            background: linear-gradient(135deg, #4285f4 0%, #34a853 50%, #fbbc05 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 10px 0;
            text-align: center;
        }
        .mcp-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        """
    ) as demo:
        
        gr.HTML("""
        <div class="gemini-header">
            <h1>🤖 Gemini + MCP - Equações Quadráticas</h1>
            <p><strong>Inteligência Artificial + Precisão Matemática</strong></p>
            <p>Digite equações em linguagem natural e receba explicações didáticas completas!</p>
        </div>
        """)
        
        with gr.Tabs():
            # Aba 1: Interface com Gemini
            with gr.TabItem("🤖 Assistente Gemini"):
                gr.Markdown("### Resolva equações quadráticas em linguagem natural")
                gr.Markdown("**Exemplos:** 'Resolva x² - 5x + 6 = 0' ou 'Encontre as raízes de 2x² + 3x - 1 = 0'")
                
                with gr.Row():
                    with gr.Column(scale=2):
                        user_input = gr.Textbox(
                            label="Digite sua equação quadrática",
                            placeholder="Ex: Resolva a equação x² - 5x + 6 = 0",
                            lines=3
                        )
                        
                        with gr.Row():
                            solve_btn = gr.Button("🚀 Resolver com Gemini", variant="primary", scale=2)
                            clear_btn = gr.Button("🗑️ Limpar", scale=1)
                        
                        # Exemplos rápidos
                        gr.Markdown("#### 📚 Exemplos Rápidos")
                        with gr.Row():
                            ex1_btn = gr.Button("x² - 5x + 6 = 0", size="sm")
                            ex2_btn = gr.Button("2x² - 4x + 1 = 0", size="sm")
                        with gr.Row():
                            ex3_btn = gr.Button("x² + x + 1 = 0", size="sm")
                            ex4_btn = gr.Button("-x² + 4x - 3 = 0", size="sm")
                    
                    with gr.Column(scale=1):
                        json_output = gr.JSON(
                            label="Resultado Técnico (JSON)",
                            show_label=True
                        )
                
                explanation_output = gr.Markdown(
                    label="Explicação Didática",
                    value="Digite uma equação quadrática acima para ver a explicação detalhada."
                )
                
                # Eventos
                solve_btn.click(
                    solve_with_gemini_mcp,
                    inputs=[user_input],
                    outputs=[explanation_output, json_output]
                )
                
                clear_btn.click(
                    lambda: ("", "", "{}"),
                    outputs=[user_input, explanation_output, json_output]
                )
                
                # Exemplos
                ex1_btn.click(lambda: "Resolva a equação x² - 5x + 6 = 0", outputs=[user_input])
                ex2_btn.click(lambda: "Encontre as raízes de 2x² - 4x + 1 = 0", outputs=[user_input])
                ex3_btn.click(lambda: "Analise a equação x² + x + 1 = 0", outputs=[user_input])
                ex4_btn.click(lambda: "Resolva -x² + 4x - 3 = 0", outputs=[user_input])
            
            # Aba 2: Interface MCP Tradicional
            with gr.TabItem("🔧 Ferramentas MCP"):
                gr.Markdown("### Interface MCP para integração com outros sistemas")
                
                with gr.Row():
                    with gr.Column():
                        a_input = gr.Textbox(label="Coeficiente a", value="1")
                        b_input = gr.Textbox(label="Coeficiente b", value="-5")
                        c_input = gr.Textbox(label="Coeficiente c", value="6")
                        mcp_solve_btn = gr.Button("Resolver (MCP)", variant="primary")
                    
                    with gr.Column():
                        mcp_output = gr.JSON(label="Resultado MCP")
                
                mcp_solve_btn.click(
                    solve_quadratic_mcp_tool,
                    inputs=[a_input, b_input, c_input],
                    outputs=[mcp_output]
                )
        
        # Informações sobre configuração
        gr.HTML("""
        <div class="mcp-info">
            <h3>📡 Configuração do Servidor</h3>
            <p><strong>URL MCP:</strong> <code>http://localhost:7861/gradio_api/mcp/sse</code></p>
            <p><strong>Status Gemini:</strong> """ + ("✅ Ativo" if gemini_server.gemini_model else "❌ Inativo") + """</p>
            <p><strong>Ferramentas Disponíveis:</strong></p>
            <ul>
                <li><code>solve_quadratic_mcp_tool</code> - Resolve equações quadráticas</li>
                <li><code>solve_with_gemini_mcp</code> - Resolve com explicação do Gemini</li>
            </ul>
        </div>
        """)
    
    return demo


def main():
    """Função principal."""
    print("🚀 Iniciando Servidor Gemini + MCP...")
    print("=" * 60)
    
    # Status das dependências
    print("📦 Status das Dependências:")
    print(f"   • python-dotenv: {'✅' if HAS_DOTENV else '❌'}")
    print(f"   • google-generativeai: {'✅' if HAS_GEMINI else '❌'}")
    print(f"   • quadratic_solver: {'✅' if HAS_QUADRATIC_SOLVER else '❌ (usando básico)'}")
    
    # Status do Gemini
    if gemini_server.gemini_model:
        print("   • Gemini Model: ✅ Configurado")
    else:
        print("   • Gemini Model: ❌ Não configurado")
        print("     💡 Verifique se GOOGLE_API_KEY está no arquivo .env")
    
    # Configurações do servidor
    host = os.getenv("MCP_SERVER_HOST", "0.0.0.0")
    port = int(os.getenv("MCP_SERVER_PORT", "7861"))
    
    print(f"\n📡 Configurações do Servidor:")
    print(f"   • Host: {host}")
    print(f"   • Porta: {port}")
    print(f"   • URL: http://localhost:{port}")
    print(f"   • MCP Endpoint: http://localhost:{port}/gradio_api/mcp/sse")
    
    # Criar e lançar interface
    demo = create_gemini_mcp_interface()
    
    print(f"\n🎯 Acesse a interface em: http://localhost:{port}")
    print("🤖 Digite equações em linguagem natural para usar o Gemini!")
    
    demo.launch(
        server_name=host,
        server_port=port,
        mcp_server=True,
        share=False,
        show_error=True,
        show_api=True
    )


if __name__ == "__main__":
    main()

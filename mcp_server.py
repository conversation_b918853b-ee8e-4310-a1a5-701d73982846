#!/usr/bin/env python3
"""
Servidor MCP (Model Context Protocol) usando Gradio para resolver equações quadráticas.

Este servidor expõe ferramentas matemáticas através do protocolo MCP, permitindo que
LLMs (como Claude) acessem funcionalidades de resolução de equações quadráticas.

Uso:
    python mcp_server.py

O servidor estará disponível em:
    http://localhost:7860/gradio_api/mcp/sse
"""

import gradio as gr
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, Tuple, List, Union
import json
import os
from src.quadratic_solver import solve_quadratic, format_solution, plot_quadratic


def solve_quadratic_equation_mcp(a: str, b: str, c: str) -> str:
    """
    Resolve uma equação quadrática da forma ax² + bx + c = 0.
    
    Esta ferramenta resolve equações de segundo grau e fornece análise completa
    incluindo discriminante, vértice, raízes e gráfico da função.
    
    Args:
        a (str): Coeficiente de x² (se a=0, será uma equação linear)
        b (str): Coeficiente de x (termo linear)
        c (str): Termo independente (constante)
    
    Returns:
        str: Solução detalhada da equação em formato JSON
    """
    try:
        # Converter strings para float
        a_float = float(a)
        b_float = float(b)
        c_float = float(c)
        
        # Resolver usando o módulo quadratic_solver
        solution = solve_quadratic(a_float, b_float, c_float)
        
        # Formatar resultado para MCP
        result = {
            "equation": f"{a_float}x² + {b_float}x + {c_float} = 0",
            "equation_type": solution['equation_type'],
            "roots_type": solution['roots_type'],
            "has_real_roots": solution['has_real_roots'],
            "roots": [],
            "analysis": {}
        }
        
        # Adicionar raízes (convertendo complexos para string se necessário)
        for root in solution['roots']:
            if isinstance(root, complex):
                result["roots"].append(f"{root.real:.6f} + {root.imag:.6f}i")
            else:
                result["roots"].append(float(root))
        
        # Adicionar análise detalhada
        if solution['discriminant'] is not None:
            result["analysis"]["discriminant"] = solution['discriminant']
        
        if solution['vertex']:
            result["analysis"]["vertex"] = {
                "x": solution['vertex'][0],
                "y": solution['vertex'][1]
            }
        
        if solution['axis_of_symmetry'] is not None:
            result["analysis"]["axis_of_symmetry"] = solution['axis_of_symmetry']
        
        result["analysis"]["y_intercept"] = solution['y_intercept']
        result["analysis"]["concavity"] = solution['concavity']
        
        if solution['factored_form']:
            result["analysis"]["factored_form"] = solution['factored_form']
        
        # Adicionar texto formatado para exibição
        result["formatted_solution"] = format_solution(solution)
        
        return json.dumps(result, indent=2, ensure_ascii=False)
        
    except ValueError as e:
        error_result = {
            "error": "Erro de entrada",
            "message": f"Os coeficientes devem ser números válidos: {str(e)}",
            "equation": f"{a}x² + {b}x + {c} = 0"
        }
        return json.dumps(error_result, indent=2, ensure_ascii=False)
    
    except Exception as e:
        error_result = {
            "error": "Erro interno",
            "message": f"Erro ao resolver a equação: {str(e)}",
            "equation": f"{a}x² + {b}x + {c} = 0"
        }
        return json.dumps(error_result, indent=2, ensure_ascii=False)


def analyze_quadratic_function(a: str, b: str, c: str) -> str:
    """
    Analisa as propriedades de uma função quadrática f(x) = ax² + bx + c.
    
    Fornece análise detalhada das propriedades da função quadrática incluindo
    domínio, imagem, crescimento, decrescimento e pontos críticos.
    
    Args:
        a (str): Coeficiente de x²
        b (str): Coeficiente de x
        c (str): Termo independente
    
    Returns:
        str: Análise completa da função em formato JSON
    """
    try:
        a_float = float(a)
        b_float = float(b)
        c_float = float(c)
        
        if a_float == 0:
            return json.dumps({
                "error": "Função linear",
                "message": "Para a=0, a função é linear: f(x) = bx + c",
                "function": f"f(x) = {b_float}x + {c_float}"
            }, indent=2, ensure_ascii=False)
        
        # Calcular propriedades da função
        vertex_x = -b_float / (2 * a_float)
        vertex_y = a_float * vertex_x**2 + b_float * vertex_x + c_float
        discriminant = b_float**2 - 4*a_float*c_float
        
        analysis = {
            "function": f"f(x) = {a_float}x² + {b_float}x + {c_float}",
            "domain": "ℝ (todos os números reais)",
            "vertex": {
                "coordinates": [vertex_x, vertex_y],
                "description": f"Vértice em ({vertex_x:.3f}, {vertex_y:.3f})"
            },
            "axis_of_symmetry": {
                "equation": f"x = {vertex_x:.3f}",
                "value": vertex_x
            },
            "concavity": {
                "direction": "para cima" if a_float > 0 else "para baixo",
                "coefficient_a": a_float
            },
            "y_intercept": {
                "point": [0, c_float],
                "description": f"Intercepta o eixo y em (0, {c_float})"
            }
        }
        
        # Análise das raízes
        if discriminant > 0:
            x1 = (-b_float + np.sqrt(discriminant)) / (2*a_float)
            x2 = (-b_float - np.sqrt(discriminant)) / (2*a_float)
            analysis["x_intercepts"] = {
                "count": 2,
                "points": [[x1, 0], [x2, 0]],
                "description": f"Intercepta o eixo x em ({x1:.3f}, 0) e ({x2:.3f}, 0)"
            }
        elif discriminant == 0:
            x = -b_float / (2*a_float)
            analysis["x_intercepts"] = {
                "count": 1,
                "points": [[x, 0]],
                "description": f"Tangente ao eixo x em ({x:.3f}, 0)"
            }
        else:
            analysis["x_intercepts"] = {
                "count": 0,
                "points": [],
                "description": "Não intercepta o eixo x (raízes complexas)"
            }
        
        # Análise do crescimento
        if a_float > 0:
            analysis["behavior"] = {
                "decreasing": f"(-∞, {vertex_x:.3f})",
                "increasing": f"({vertex_x:.3f}, +∞)",
                "minimum": f"Mínimo global em x = {vertex_x:.3f}, f({vertex_x:.3f}) = {vertex_y:.3f}",
                "image": f"[{vertex_y:.3f}, +∞)" if vertex_y >= 0 else f"[{vertex_y:.3f}, +∞)"
            }
        else:
            analysis["behavior"] = {
                "increasing": f"(-∞, {vertex_x:.3f})",
                "decreasing": f"({vertex_x:.3f}, +∞)",
                "maximum": f"Máximo global em x = {vertex_x:.3f}, f({vertex_x:.3f}) = {vertex_y:.3f}",
                "image": f"(-∞, {vertex_y:.3f}]"
            }
        
        # Informações adicionais
        analysis["discriminant"] = {
            "value": discriminant,
            "interpretation": "Duas raízes reais distintas" if discriminant > 0 
                           else "Uma raiz real (dupla)" if discriminant == 0 
                           else "Duas raízes complexas conjugadas"
        }
        
        return json.dumps(analysis, indent=2, ensure_ascii=False)
        
    except ValueError as e:
        return json.dumps({
            "error": "Erro de entrada",
            "message": f"Os coeficientes devem ser números válidos: {str(e)}"
        }, indent=2, ensure_ascii=False)
    
    except Exception as e:
        return json.dumps({
            "error": "Erro interno",
            "message": f"Erro na análise: {str(e)}"
        }, indent=2, ensure_ascii=False)


def find_quadratic_from_roots(root1: str, root2: str, a: str = "1") -> str:
    """
    Encontra a equação quadrática dados suas raízes.
    
    Constrói uma equação quadrática ax² + bx + c = 0 a partir de suas raízes
    usando a forma fatorada a(x - r1)(x - r2) = 0.
    
    Args:
        root1 (str): Primeira raiz da equação
        root2 (str): Segunda raiz da equação  
        a (str): Coeficiente principal (padrão: 1)
    
    Returns:
        str: Equação quadrática e análise em formato JSON
    """
    try:
        r1 = float(root1)
        r2 = float(root2)
        a_coef = float(a)
        
        # Calcular coeficientes: a(x - r1)(x - r2) = a(x² - (r1+r2)x + r1*r2)
        b_coef = -a_coef * (r1 + r2)
        c_coef = a_coef * r1 * r2
        
        result = {
            "given_roots": [r1, r2],
            "coefficient_a": a_coef,
            "equation": {
                "standard_form": f"{a_coef}x² + {b_coef}x + {c_coef} = 0",
                "factored_form": f"{a_coef}(x - {r1})(x - {r2}) = 0",
                "coefficients": {
                    "a": a_coef,
                    "b": b_coef,
                    "c": c_coef
                }
            },
            "verification": {
                "sum_of_roots": r1 + r2,
                "product_of_roots": r1 * r2,
                "discriminant": b_coef**2 - 4*a_coef*c_coef,
                "vertex_x": (r1 + r2) / 2,
                "vertex_y": a_coef * ((r1 + r2) / 2 - r1) * ((r1 + r2) / 2 - r2)
            }
        }
        
        # Verificação das fórmulas de Vieta
        result["vieta_formulas"] = {
            "sum_formula": f"r1 + r2 = -b/a = {-b_coef/a_coef:.6f}",
            "product_formula": f"r1 × r2 = c/a = {c_coef/a_coef:.6f}",
            "verification": "✓ Fórmulas de Vieta confirmadas"
        }
        
        return json.dumps(result, indent=2, ensure_ascii=False)
        
    except ValueError as e:
        return json.dumps({
            "error": "Erro de entrada",
            "message": f"As raízes e coeficiente devem ser números válidos: {str(e)}"
        }, indent=2, ensure_ascii=False)
    
    except Exception as e:
        return json.dumps({
            "error": "Erro interno", 
            "message": f"Erro ao construir equação: {str(e)}"
        }, indent=2, ensure_ascii=False)


# Interface Gradio para o servidor MCP
def create_mcp_interface():
    """Cria a interface Gradio que funcionará como servidor MCP."""
    
    with gr.Blocks(
        title="Servidor MCP - Equações Quadráticas",
        theme=gr.themes.Soft(),
        css="""
        .gradio-container {
            max-width: 1200px !important;
        }
        .mcp-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 10px 0;
        }
        """
    ) as demo:
        
        gr.HTML("""
        <div class="mcp-info">
            <h1>🔧 Servidor MCP - Equações Quadráticas</h1>
            <p><strong>Model Context Protocol Server</strong> para resolução de equações quadráticas</p>
            <p>Este servidor expõe ferramentas matemáticas que podem ser usadas por LLMs como Claude, Cursor, ou Cline.</p>
        </div>
        """)
        
        with gr.Tabs():
            # Aba 1: Resolver Equação
            with gr.TabItem("🔍 Resolver Equação"):
                gr.Markdown("### Ferramenta: solve_quadratic_equation_mcp")
                
                with gr.Row():
                    with gr.Column():
                        a1 = gr.Textbox(label="Coeficiente a", value="1", placeholder="1")
                        b1 = gr.Textbox(label="Coeficiente b", value="-5", placeholder="-5") 
                        c1 = gr.Textbox(label="Coeficiente c", value="6", placeholder="6")
                        solve_btn = gr.Button("Resolver Equação", variant="primary")
                    
                    with gr.Column():
                        result1 = gr.JSON(label="Resultado JSON")
                
                solve_btn.click(
                    solve_quadratic_equation_mcp,
                    inputs=[a1, b1, c1],
                    outputs=[result1]
                )
            
            # Aba 2: Analisar Função
            with gr.TabItem("📊 Analisar Função"):
                gr.Markdown("### Ferramenta: analyze_quadratic_function")
                
                with gr.Row():
                    with gr.Column():
                        a2 = gr.Textbox(label="Coeficiente a", value="1", placeholder="1")
                        b2 = gr.Textbox(label="Coeficiente b", value="-4", placeholder="-4")
                        c2 = gr.Textbox(label="Coeficiente c", value="3", placeholder="3")
                        analyze_btn = gr.Button("Analisar Função", variant="primary")
                    
                    with gr.Column():
                        result2 = gr.JSON(label="Análise JSON")
                
                analyze_btn.click(
                    analyze_quadratic_function,
                    inputs=[a2, b2, c2],
                    outputs=[result2]
                )
            
            # Aba 3: Construir Equação
            with gr.TabItem("🏗️ Construir Equação"):
                gr.Markdown("### Ferramenta: find_quadratic_from_roots")
                
                with gr.Row():
                    with gr.Column():
                        root1 = gr.Textbox(label="Raiz 1", value="2", placeholder="2")
                        root2 = gr.Textbox(label="Raiz 2", value="3", placeholder="3")
                        a3 = gr.Textbox(label="Coeficiente a", value="1", placeholder="1")
                        construct_btn = gr.Button("Construir Equação", variant="primary")
                    
                    with gr.Column():
                        result3 = gr.JSON(label="Equação Construída")
                
                construct_btn.click(
                    find_quadratic_from_roots,
                    inputs=[root1, root2, a3],
                    outputs=[result3]
                )
        
        # Informações sobre MCP
        gr.HTML("""
        <div class="mcp-info">
            <h3>📡 Configuração MCP</h3>
            <p><strong>URL do Servidor:</strong> <code>http://localhost:7861/gradio_api/mcp/sse</code></p>
            <p><strong>Ferramentas Disponíveis:</strong></p>
            <ul>
                <li><code>solve_quadratic_equation_mcp</code> - Resolve equações quadráticas</li>
                <li><code>analyze_quadratic_function</code> - Analisa propriedades da função</li>
                <li><code>find_quadratic_from_roots</code> - Constrói equação a partir das raízes</li>
            </ul>
            <p><strong>Configuração para Claude Desktop:</strong></p>
            <pre>{
  "mcpServers": {
    "quadratic-solver": {
      "url": "http://localhost:7861/gradio_api/mcp/sse"
    }
  }
}</pre>
        </div>
        """)
    
    return demo


def main():
    """Função principal para iniciar o servidor MCP."""
    print("🚀 Iniciando Servidor MCP - Equações Quadráticas...")
    print("=" * 60)
    
    # Verificar se o módulo quadratic_solver está disponível
    try:
        from src.quadratic_solver import solve_quadratic
        print("✅ Módulo quadratic_solver carregado com sucesso")
    except ImportError as e:
        print(f"❌ Erro ao importar módulo: {e}")
        print("💡 Certifique-se de que o arquivo src/quadratic_solver.py existe")
        return
    
    # Criar e lançar a interface
    demo = create_mcp_interface()
    
    print("\n📡 Configurações do Servidor MCP:")
    print(f"   • URL: http://localhost:7861/gradio_api/mcp/sse")
    print(f"   • Schema: http://localhost:7861/gradio_api/mcp/schema")
    print(f"   • Interface Web: http://localhost:7861")
    
    print("\n🔧 Ferramentas Disponíveis:")
    print("   • solve_quadratic_equation_mcp")
    print("   • analyze_quadratic_function") 
    print("   • find_quadratic_from_roots")
    
    print("\n🎯 Para usar com Claude Desktop, adicione esta configuração:")
    print("""
{
  "mcpServers": {
    "quadratic-solver": {
      "url": "http://localhost:7861/gradio_api/mcp/sse"
    }
  }
}
    """)
    
    # Lançar com MCP habilitado
    demo.launch(
        server_name="0.0.0.0",
        server_port=7861,  # Porta diferente para evitar conflito
        mcp_server=True,  # Habilita o servidor MCP
        share=False,
        show_error=True,
        show_api=True
    )


if __name__ == "__main__":
    main()
